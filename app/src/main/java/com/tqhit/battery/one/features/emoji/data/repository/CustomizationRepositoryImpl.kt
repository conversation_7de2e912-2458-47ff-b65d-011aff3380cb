package com.tqhit.battery.one.features.emoji.data.repository
import com.tqhit.battery.one.utils.BatteryLogger

import android.util.Log
import com.google.gson.Gson
import com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.model.UserPreferences
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Default implementation of CustomizationRepository using DataStore.
 * 
 * Provides persistent storage for user customization data with reactive Flow streams.
 * Integrates with CustomizationDataStore for type-safe data persistence and follows
 * the established repository pattern from the stats module.
 * 
 * Key features:
 * - Reactive Flow-based data streams
 * - Comprehensive error handling and recovery
 * - Data validation and sanitization
 * - Export/import functionality for backup/restore
 * - Usage analytics and history tracking
 */
@Singleton
class CustomizationRepositoryImpl @Inject constructor(
    private val dataStore: CustomizationDataStore,
    private val gson: Gson
) : CustomizationRepository {
    
    companion object {
        private const val TAG = "CustomizationRepo"
        private const val EXPORT_VERSION = 1
    }
    
    override val userCustomizationFlow: Flow<UserCustomization> = dataStore.userCustomizationFlow
    
    override val customizationConfigFlow: Flow<CustomizationConfig> = dataStore.customizationConfigFlow
    
    override val userPreferencesFlow: Flow<UserPreferences> = dataStore.userPreferencesFlow
    
    override suspend fun getCurrentCustomizationConfig(): CustomizationConfig {
        return try {
            customizationConfigFlow.first()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to get current customization config", e)
            CustomizationConfig.createDefault()
        }
    }
    
    override suspend fun getCurrentUserCustomization(): UserCustomization {
        return try {
            userCustomizationFlow.first()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to get current user customization", e)
            UserCustomization.createDefault()
        }
    }
    
    override suspend fun saveCustomizationConfig(config: CustomizationConfig): Result<Unit> {
        return try {
            val validatedConfig = config.validated()
            val result = dataStore.saveCustomizationConfig(validatedConfig)
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully saved customization config: ${validatedConfig.selectedStyleId}")
            } else {
                BatteryLogger.e(TAG, "Failed to save customization config", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception saving customization config", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to save customization config", e))
        }
    }
    
    override suspend fun saveUserCustomization(userCustomization: UserCustomization): Result<Unit> {
        return try {
            val validatedCustomization = userCustomization.copy(
                customizationConfig = userCustomization.customizationConfig.validated()
            )
            val result = dataStore.saveUserCustomization(validatedCustomization)
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully saved user customization")
            } else {
                BatteryLogger.e(TAG, "Failed to save user customization", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception saving user customization", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to save user customization", e))
        }
    }
    
    override suspend fun saveUserPreferences(preferences: UserPreferences): Result<Unit> {
        return try {
            val result = dataStore.saveUserPreferences(preferences)
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully saved user preferences")
            } else {
                BatteryLogger.e(TAG, "Failed to save user preferences", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception saving user preferences", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to save user preferences", e))
        }
    }
    
    override suspend fun updatePermissionStates(
        hasAccessibilityPermission: Boolean,
        hasOverlayPermission: Boolean,
        isServiceEnabled: Boolean
    ): Result<Unit> {
        return try {
            val result = dataStore.updatePermissionStates(
                hasAccessibilityPermission,
                hasOverlayPermission,
                isServiceEnabled
            )
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully updated permission states: accessibility=$hasAccessibilityPermission, overlay=$hasOverlayPermission, service=$isServiceEnabled")
            } else {
                BatteryLogger.e(TAG, "Failed to update permission states", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception updating permission states", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to update permission states", e))
        }
    }
    
    override suspend fun setFeatureEnabled(enabled: Boolean): Result<Unit> {
        return try {
            val currentConfig = getCurrentCustomizationConfig()
            val updatedConfig = currentConfig.withFeatureEnabled(enabled)
            saveCustomizationConfig(updatedConfig)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception setting feature enabled state", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to set feature enabled state", e))
        }
    }
    
    override suspend fun recordStyleUsage(styleId: String): Result<Unit> {
        return try {
            val currentCustomization = getCurrentUserCustomization()
            val updatedHistory = currentCustomization.usageHistory.withStyleUsed(styleId)
            val updatedCustomization = currentCustomization.copy(usageHistory = updatedHistory)
            
            saveUserCustomization(updatedCustomization)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception recording style usage", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to record style usage", e))
        }
    }
    
    override suspend fun resetToDefaults(preservePreferences: Boolean): Result<Unit> {
        return try {
            val currentCustomization = getCurrentUserCustomization()
            val defaultCustomization = if (preservePreferences) {
                UserCustomization.createDefault().copy(
                    userPreferences = currentCustomization.userPreferences
                )
            } else {
                UserCustomization.createDefault()
            }
            
            val result = saveUserCustomization(defaultCustomization)
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully reset to defaults (preservePreferences=$preservePreferences)")
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception resetting to defaults", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to reset to defaults", e))
        }
    }
    
    override suspend fun clearAllData(): Result<Unit> {
        return try {
            val result = dataStore.clearAllData()
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully cleared all data")
            } else {
                BatteryLogger.e(TAG, "Failed to clear all data", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception clearing all data", e)
            Result.failure(CustomizationRepositoryException.PersistenceException("Failed to clear all data", e))
        }
    }
    
    override suspend fun validateAndFixConfiguration(): Result<List<String>> {
        return try {
            val currentCustomization = getCurrentUserCustomization()
            val issues = mutableListOf<String>()
            var needsUpdate = false
            
            // Validate customization config
            val config = currentCustomization.customizationConfig
            if (!config.isValid()) {
                issues.add("Invalid customization configuration detected")
                needsUpdate = true
            }
            
            // Validate style config
            val styleConfig = config.styleConfig
            if (!styleConfig.isValid()) {
                issues.add("Invalid style configuration detected")
                needsUpdate = true
            }
            
            // Apply fixes if needed
            if (needsUpdate) {
                val fixedCustomization = currentCustomization.copy(
                    customizationConfig = config.validated()
                )
                
                val saveResult = saveUserCustomization(fixedCustomization)
                if (saveResult.isSuccess) {
                    issues.add("Configuration automatically fixed and saved")
                    BatteryLogger.d(TAG, "Configuration validated and fixed")
                } else {
                    issues.add("Failed to save fixed configuration")
                    BatteryLogger.e(TAG, "Failed to save fixed configuration", saveResult.exceptionOrNull())
                }
            } else {
                BatteryLogger.d(TAG, "Configuration validation passed")
            }
            
            Result.success(issues)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception during configuration validation", e)
            Result.failure(CustomizationRepositoryException.ValidationException("Failed to validate configuration", e))
        }
    }

    override suspend fun exportCustomizationData(): Result<String> {
        return try {
            val currentCustomization = getCurrentUserCustomization()

            val exportData = mapOf(
                "version" to EXPORT_VERSION,
                "timestamp" to System.currentTimeMillis(),
                "userCustomization" to currentCustomization
            )

            val serializedData = gson.toJson(exportData)
            BatteryLogger.d(TAG, "Successfully exported customization data")
            Result.success(serializedData)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception exporting customization data", e)
            Result.failure(CustomizationRepositoryException.SerializationException("Failed to export customization data", e))
        }
    }

    override suspend fun importCustomizationData(
        serializedData: String,
        overwriteExisting: Boolean
    ): Result<Unit> {
        return try {
            val exportData = gson.fromJson(serializedData, Map::class.java)
            val version = (exportData["version"] as? Double)?.toInt() ?: 0

            if (version != EXPORT_VERSION) {
                return Result.failure(
                    CustomizationRepositoryException.SerializationException(
                        "Unsupported export version: $version (expected: $EXPORT_VERSION)"
                    )
                )
            }

            val userCustomizationData = exportData["userCustomization"] as? Map<*, *>
                ?: return Result.failure(
                    CustomizationRepositoryException.SerializationException("Invalid export data format")
                )

            val importedCustomization = gson.fromJson(
                gson.toJson(userCustomizationData),
                UserCustomization::class.java
            )

            // Validate imported data
            if (!importedCustomization.customizationConfig.isValid()) {
                return Result.failure(
                    CustomizationRepositoryException.ValidationException("Imported customization data is invalid")
                )
            }

            // Merge with existing data if not overwriting
            val finalCustomization = if (overwriteExisting) {
                importedCustomization
            } else {
                val currentCustomization = getCurrentUserCustomization()
                // Merge logic: keep current permissions and preferences, import config and history
                currentCustomization.copy(
                    customizationConfig = importedCustomization.customizationConfig,
                    usageHistory = importedCustomization.usageHistory
                )
            }

            val result = saveUserCustomization(finalCustomization)

            if (result.isSuccess) {
                BatteryLogger.d(TAG, "Successfully imported customization data (overwrite=$overwriteExisting)")
            }

            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Exception importing customization data", e)
            Result.failure(CustomizationRepositoryException.SerializationException("Failed to import customization data", e))
        }
    }
}

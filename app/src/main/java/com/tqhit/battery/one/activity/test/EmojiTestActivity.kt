package com.tqhit.battery.one.activity.test

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityEmojiTestBinding
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * Test Activity for Emoji Feature
 * This activity is used to test the emoji feature independently of the main app
 */
@AndroidEntryPoint
class EmojiTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityEmojiTestBinding
    private val emojiViewModel: CustomizeViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        BatteryLogger.d("EmojiTestActivity", "onCreate: Starting emoji test activity")
        
        binding = ActivityEmojiTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
        
        BatteryLogger.d("EmojiTestActivity", "onCreate: Emoji test activity setup complete")
    }
    
    private fun setupUI() {
        BatteryLogger.d("EmojiTestActivity", "setupUI: Setting up UI components")
        
        binding.apply {
            // Set up test buttons
            btnTestEmojiLoad.setOnClickListener {
                BatteryLogger.d("EmojiTestActivity", "btnTestEmojiLoad clicked")
                testEmojiLoad()
            }
            
            btnTestEmojiSelection.setOnClickListener {
                BatteryLogger.d("EmojiTestActivity", "btnTestEmojiSelection clicked")
                testEmojiSelection()
            }
            
            btnTestEmojiDisplay.setOnClickListener {
                BatteryLogger.d("EmojiTestActivity", "btnTestEmojiDisplay clicked")
                testEmojiDisplay()
            }
        }
    }
    
    private fun observeViewModel() {
        BatteryLogger.d("EmojiTestActivity", "observeViewModel: Setting up observers")
        
        lifecycleScope.launch {
            emojiViewModel.uiState.collect { state ->
                BatteryLogger.d("EmojiTestActivity", "UI State updated: $state")
                updateUI(state)
            }
        }
    }
    
    private fun updateUI(state: EmojiCustomizeViewModel.UiState) {
        binding.apply {
            tvStatus.text = "Status: ${state.isLoading}"
            tvSelectedEmoji.text = "Selected: ${state.selectedEmoji?.unicode ?: "None"}"
            tvAvailableCount.text = "Available: ${state.availableEmojis.size}"
            
            if (state.error != null) {
                tvError.text = "Error: ${state.error}"
                tvError.visibility = android.view.View.VISIBLE
            } else {
                tvError.visibility = android.view.View.GONE
            }
        }
    }
    
    private fun testEmojiLoad() {
        BatteryLogger.d("EmojiTestActivity", "testEmojiLoad: Testing emoji loading")
        emojiViewModel.loadAvailableEmojis()
    }
    
    private fun testEmojiSelection() {
        BatteryLogger.d("EmojiTestActivity", "testEmojiSelection: Testing emoji selection")
        // Test with a sample emoji
        emojiViewModel.selectEmoji("😀")
    }
    
    private fun testEmojiDisplay() {
        BatteryLogger.d("EmojiTestActivity", "testEmojiDisplay: Testing emoji display")
        // Test display functionality
        emojiViewModel.previewEmoji("🔋")
    }
}

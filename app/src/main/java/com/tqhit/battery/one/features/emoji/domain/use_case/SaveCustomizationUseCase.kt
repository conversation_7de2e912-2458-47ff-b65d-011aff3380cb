package com.tqhit.battery.one.features.emoji.domain.use_case
import com.tqhit.battery.one.utils.BatteryLogger

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for saving user customization configurations.
 * 
 * Handles the business logic for validating and persisting user customization data.
 * Integrates with both BatteryStyleRepository for style validation and 
 * CustomizationRepository for persistence.
 * 
 * Key responsibilities:
 * - Validate customization data before saving
 * - Ensure selected styles exist and are accessible
 * - Handle premium style validation
 * - Update usage history and analytics
 * - Provide comprehensive error handling
 */
@Singleton
class SaveCustomizationUseCase @Inject constructor(
    private val customizationRepository: CustomizationRepository,
    private val batteryStyleRepository: BatteryStyleRepository
) {
    
    companion object {
        private const val TAG = "SaveCustomizationUseCase"
    }
    
    /**
     * Saves a complete customization configuration.
     * Validates the configuration and updates usage history.
     * 
     * @param config The CustomizationConfig to save
     * @return Result indicating success or failure with error details
     */
    suspend fun saveCustomizationConfig(config: CustomizationConfig): Result<Unit> {
        return try {
            BatteryLogger.d(TAG, "SAVE_CONFIG: Saving customization config for style: ${config.selectedStyleId}")
            
            // Validate the configuration
            val validationResult = validateCustomizationConfig(config)
            if (validationResult.isFailure) {
                return validationResult
            }
            
            // Save the configuration
            val saveResult = customizationRepository.saveCustomizationConfig(config)
            
            if (saveResult.isSuccess) {
                // Record style usage for analytics
                if (config.selectedStyleId.isNotBlank()) {
                    customizationRepository.recordStyleUsage(config.selectedStyleId)
                }
                BatteryLogger.d(TAG, "SAVE_CONFIG: Successfully saved customization config")
            } else {
                BatteryLogger.e(TAG, "SAVE_CONFIG: Failed to save customization config", saveResult.exceptionOrNull())
            }
            
            saveResult
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "SAVE_CONFIG: Exception saving customization config", e)
            Result.failure(SaveCustomizationException("Failed to save customization config", e))
        }
    }
    
    /**
     * Saves a complete user customization state.
     * 
     * @param userCustomization The UserCustomization to save
     * @return Result indicating success or failure with error details
     */
    suspend fun saveUserCustomization(userCustomization: UserCustomization): Result<Unit> {
        return try {
            BatteryLogger.d(TAG, "SAVE_USER: Saving user customization")
            
            // Validate the customization configuration
            val validationResult = validateCustomizationConfig(userCustomization.customizationConfig)
            if (validationResult.isFailure) {
                return validationResult
            }
            
            // Save the user customization
            val saveResult = customizationRepository.saveUserCustomization(userCustomization)
            
            if (saveResult.isSuccess) {
                BatteryLogger.d(TAG, "SAVE_USER: Successfully saved user customization")
            } else {
                BatteryLogger.e(TAG, "SAVE_USER: Failed to save user customization", saveResult.exceptionOrNull())
            }
            
            saveResult
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "SAVE_USER: Exception saving user customization", e)
            Result.failure(SaveCustomizationException("Failed to save user customization", e))
        }
    }
    
    /**
     * Saves customization from a selected battery style.
     * Creates a new configuration based on the style and saves it.
     * 
     * @param batteryStyle The BatteryStyle to create configuration from
     * @param enableFeature Whether to enable the feature immediately
     * @return Result indicating success or failure with error details
     */
    suspend fun saveCustomizationFromStyle(
        batteryStyle: BatteryStyle,
        enableFeature: Boolean = false
    ): Result<Unit> {
        return try {
            BatteryLogger.d(TAG, "SAVE_FROM_STYLE: Creating customization from style: ${batteryStyle.name}")
            
            // Validate the battery style
            if (!batteryStyle.isValid()) {
                return Result.failure(SaveCustomizationException("Invalid battery style provided"))
            }
            
            // Check if style is premium and user has access
            if (batteryStyle.isPremium) {
                val hasAccess = checkPremiumStyleAccess(batteryStyle)
                if (!hasAccess) {
                    return Result.failure(SaveCustomizationException("Premium style access required"))
                }
            }
            
            // Create configuration from style
            val config = CustomizationConfig.fromBatteryStyle(batteryStyle)
                .withFeatureEnabled(enableFeature)
            
            // Save the configuration
            saveCustomizationConfig(config)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "SAVE_FROM_STYLE: Exception saving customization from style", e)
            Result.failure(SaveCustomizationException("Failed to save customization from style", e))
        }
    }
    
    /**
     * Updates only the style configuration (visual settings).
     * Preserves other customization settings.
     * 
     * @param styleConfig The BatteryStyleConfig to apply
     * @return Result indicating success or failure with error details
     */
    suspend fun updateStyleConfig(styleConfig: BatteryStyleConfig): Result<Unit> {
        return try {
            BatteryLogger.d(TAG, "UPDATE_STYLE_CONFIG: Updating style configuration")
            
            // Validate the style configuration
            if (!styleConfig.isValid()) {
                return Result.failure(SaveCustomizationException("Invalid style configuration"))
            }
            
            // Get current configuration and update style config
            val currentConfig = customizationRepository.getCurrentCustomizationConfig()
            val updatedConfig = currentConfig.withStyleConfig(styleConfig)
            
            // Save the updated configuration
            saveCustomizationConfig(updatedConfig)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "UPDATE_STYLE_CONFIG: Exception updating style configuration", e)
            Result.failure(SaveCustomizationException("Failed to update style configuration", e))
        }
    }
    
    /**
     * Enables or disables the emoji battery feature.
     * 
     * @param enabled Whether the feature should be enabled
     * @return Result indicating success or failure with error details
     */
    suspend fun setFeatureEnabled(enabled: Boolean): Result<Unit> {
        return try {
            BatteryLogger.d(TAG, "SET_FEATURE_ENABLED: Setting feature enabled to: $enabled")
            
            val result = customizationRepository.setFeatureEnabled(enabled)
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "SET_FEATURE_ENABLED: Successfully set feature enabled to: $enabled")
            } else {
                BatteryLogger.e(TAG, "SET_FEATURE_ENABLED: Failed to set feature enabled", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "SET_FEATURE_ENABLED: Exception setting feature enabled", e)
            Result.failure(SaveCustomizationException("Failed to set feature enabled state", e))
        }
    }
    
    /**
     * Validates a customization configuration.
     * 
     * @param config The CustomizationConfig to validate
     * @return Result indicating validation success or failure
     */
    private suspend fun validateCustomizationConfig(config: CustomizationConfig): Result<Unit> {
        return try {
            // Basic validation
            if (!config.isValid()) {
                return Result.failure(SaveCustomizationException("Customization configuration is invalid"))
            }
            
            // Validate selected style exists if specified
            if (config.selectedStyleId.isNotBlank()) {
                val styles = batteryStyleRepository.batteryStylesFlow.first()
                val selectedStyle = styles.find { it.id == config.selectedStyleId }
                
                if (selectedStyle == null) {
                    return Result.failure(SaveCustomizationException("Selected style not found: ${config.selectedStyleId}"))
                }
                
                // Check premium access if needed
                if (selectedStyle.isPremium) {
                    val hasAccess = checkPremiumStyleAccess(selectedStyle)
                    if (!hasAccess) {
                        return Result.failure(SaveCustomizationException("Premium style access required"))
                    }
                }
            }
            
            BatteryLogger.d(TAG, "VALIDATE: Configuration validation passed")
            Result.success(Unit)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "VALIDATE: Exception during validation", e)
            Result.failure(SaveCustomizationException("Configuration validation failed", e))
        }
    }
    
    /**
     * Checks if user has access to a premium style.
     * TODO: Implement actual premium access logic in Phase 5
     * 
     * @param style The BatteryStyle to check access for
     * @return true if user has access, false otherwise
     */
    private suspend fun checkPremiumStyleAccess(style: BatteryStyle): Boolean {
        // TODO: Implement premium access checking logic
        // For now, allow access to all styles during development
        BatteryLogger.d(TAG, "PREMIUM_CHECK: Premium access check for style: ${style.name} (allowing for development)")
        return true
    }
}

/**
 * Exception thrown by SaveCustomizationUseCase operations.
 */
class SaveCustomizationException(message: String, cause: Throwable? = null) : Exception(message, cause)
